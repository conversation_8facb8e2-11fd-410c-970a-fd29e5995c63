import Text6 from '@/components/CustomText';
import LanguageDropdown from '@/components/LanguageDropdown';

import AntDesign from '@expo/vector-icons/AntDesign';
import { StyleSheet, View } from 'react-native';

export default function MainScreen() {
  return (
    <View style={styles.container}>
      {/* Top row with two icons spaced between */}
      <View style={styles.topRow}>
        <LanguageDropdown iconSize={24} iconColor="#333" />
        <AntDesign name="book" size={24} color="#333" />
      </View>

      {/* Main content */}
      <View style={styles.content}>
        <Text6>Hello World - Main Screen</Text6>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60, // Safe area padding for status bar
    paddingBottom: 20,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
